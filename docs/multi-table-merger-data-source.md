# Multi-Table Merger Data Source Type

## Overview

The Multi-Table Merger is a new data source type that supports merging data from multiple tables into a unified dataset with configurable mapping rules. This data source type is designed as a reusable component that can support unified subscription system requirements without being hard-coded specifically for that system.

## Core Features

### 1. Multi-Table Selection with Wildcard Support
- **Wildcard Patterns**: Select multiple tables using patterns like `subscription_*`, `user_data_*`
- **Explicit Table Selection**: Add specific tables that don't match wildcard patterns
- **Dynamic Discovery**: Tables matching patterns are automatically discovered and included
- **Real-time Preview**: See which tables match your patterns as you type

### 2. Three Column Mapping Methods

#### Like-for-like Mapping
- **Description**: Direct column name matching across tables
- **Configuration**: No manual configuration required
- **Use Case**: When tables have identical column names that should be merged
- **Behavior**: Columns with the same name across tables are automatically merged

#### Manual Mapping
- **Description**: User-defined column mappings between source tables and output columns
- **Configuration**: Define custom mappings with merge strategies
- **Use Case**: When you need precise control over how columns are combined
- **Features**:
  - Multiple source columns can map to one output column
  - Configurable merge strategies (first non-empty, concatenate, sum, average, etc.)
  - Custom separators for concatenation

#### Unified Field Mapper
- **Description**: Automatic intelligent mapping using existing `unified_field_mapper` class rules
- **Configuration**: Review and enable/disable suggested mappings
- **Use Case**: When working with subscription or customer data that follows standard patterns
- **Features**:
  - Automatic field recognition and normalization
  - Confidence scoring for mapping suggestions
  - Post-processing (date parsing, status calculation, etc.)

### 3. Conditional Feature Availability
- **Column Mapping**: Only shown for manual and unified_field_mapper methods
- **Advanced Features**: Available after column mapping is configured
- **Progressive Disclosure**: UI adapts based on selected mapping method

### 4. Configuration Helper Features
- **Reference Table**: Select an existing table to assist with column mapping setup
- **Data Preview**: See merged data with applied mappings in real-time
- **Table Summary**: Visual overview of included tables and their column counts

## Architecture

### Components

#### Core Components
- `multi-table-merger-configuration.edge.php` - Main configuration interface
- `wildcard-pattern-item.edge.php` - Individual wildcard pattern management
- `explicit-table-item.edge.php` - Individual explicit table management
- `multi-table-column-mapping.edge.php` - Column mapping configuration
- `column-mapping-item.edge.php` - Individual column mapping management

#### Integration Points
- `data-source-builder.edge.php` - Extended to support multi-table merger type
- `data_source_manager.class.php` - Extended with multi-table merge functionality
- `data_sources.api.php` - New API endpoints for multi-table operations
- `unified_field_mapper.class.php` - Integrated for automatic mapping

### Database Schema

New columns added to `autobooks_data_sources` table:
- `data_source_type` - Type identifier ('standard' or 'multi_table_merger')
- `table_patterns` - JSON array of wildcard patterns
- `explicit_tables` - JSON array of explicitly selected tables
- `resolved_tables` - JSON array of resolved table names
- `mapping_method` - Column mapping method
- `column_mappings` - JSON array of manual column mappings
- `unified_mappings` - JSON object of unified field mapper mappings
- `reference_table` - Reference table name for mapping assistance

## API Endpoints

### Multi-Table Merger Specific
- `update_data_source_type` - Switch between standard and multi-table merger
- `add_wildcard_pattern` - Add new wildcard pattern
- `remove_wildcard_pattern` - Remove wildcard pattern
- `update_pattern_matches` - Update pattern match preview
- `add_explicit_table` - Add explicit table selection
- `remove_explicit_table` - Remove explicit table
- `update_mapping_method` - Change column mapping method
- `update_reference_table` - Update reference table selection
- `add_column_mapping` - Add new column mapping
- `remove_column_mapping` - Remove column mapping
- `add_source_column` - Add source column to mapping
- `remove_source_column` - Remove source column from mapping
- `preview_merged_data` - Generate merged data preview

## Usage Examples

### Example 1: Subscription Data Merger
```php
// Configuration for merging subscription tables
$config = [
    'name' => 'Unified Subscription Data',
    'data_source_type' => 'multi_table_merger',
    'table_patterns' => ['subscription_*', 'customer_*'],
    'explicit_tables' => ['autodesk_accounts'],
    'mapping_method' => 'unified_field_mapper',
    'category' => 'data_table'
];
```

### Example 2: Manual Column Mapping
```php
// Manual mapping configuration
$column_mappings = [
    [
        'source_columns' => ['table1.name', 'table2.company_name'],
        'output_column' => 'customer_name',
        'merge_strategy' => 'first_non_empty'
    ],
    [
        'source_columns' => ['table1.email', 'table2.contact_email'],
        'output_column' => 'email_address',
        'merge_strategy' => 'first_non_empty'
    ]
];
```

### Example 3: Wildcard Pattern Usage
```php
// Patterns that automatically include matching tables
$patterns = [
    'subscription_*',    // Matches: subscription_data, subscription_history, etc.
    'user_data_*',      // Matches: user_data_2023, user_data_2024, etc.
    'autodesk_*'        // Matches: autodesk_accounts, autodesk_subscriptions, etc.
];
```

## Merge Strategies

### Available Strategies
- **first_non_empty**: Use the first non-empty value found
- **concatenate**: Join values with a separator (default: ', ')
- **sum**: Sum numeric values
- **average**: Calculate average of numeric values
- **max**: Use maximum value
- **min**: Use minimum value
- **count**: Count non-empty values

### Strategy Selection Guidelines
- Use `first_non_empty` for unique identifiers and primary data
- Use `concatenate` for combining descriptive text
- Use `sum` for quantities and totals
- Use `average` for rates and percentages
- Use `max`/`min` for date ranges and limits

## Integration with Existing Systems

### Data Source Builder
- Seamlessly integrates with existing data source builder UI
- Maintains compatibility with standard multi-table data sources
- Uses established HTMX patterns for dynamic UI updates

### Unified Field Mapper
- Leverages existing field mapping rules and patterns
- Provides automatic normalization for subscription data
- Supports custom field mapping extensions

### Template System
- Compatible with existing Edge template system
- Follows established component patterns
- Supports hilt template conventions

## Best Practices

### Pattern Design
- Use specific patterns to avoid unintended table inclusion
- Test patterns with preview functionality before saving
- Consider future table naming when designing patterns

### Column Mapping
- Start with unified_field_mapper for standard data types
- Use manual mapping for complex business logic
- Always preview merged data before finalizing configuration

### Performance Considerations
- Limit the number of tables in a single merger
- Use appropriate merge strategies for data types
- Consider indexing on commonly merged columns

## Troubleshooting

### Common Issues
1. **No matching tables**: Check pattern syntax and table naming
2. **Empty preview**: Verify tables contain data and mappings are correct
3. **Mapping conflicts**: Review column mappings for overlapping source columns
4. **Performance issues**: Reduce table count or add database indexes

### Debug Information
- Use data preview to verify merge results
- Check resolved tables list in configuration
- Review API responses for error details
- Monitor database query performance

## Future Enhancements

### Planned Features
- Advanced filtering on merged data
- Custom merge strategies
- Scheduled data refresh
- Export functionality
- Performance optimization

### Extension Points
- Custom mapping methods
- Additional merge strategies
- Integration with external data sources
- Advanced preview options
