<?php namespace edgeTemplate\view_d7ac63c0c8832f2d406d89cfcbe62536;use edge\Edge;?><?php extract(['edge_manifest' => array (
)]);; ?>
<div class="p-5">
  
  

  
  <div id="card-body">
    <?= Edge::render('forms-button', ["id" => "Get_Products", "label" => "Get Products", "hx-post" => "" . APP_ROOT . "/api/system/system/update_products", "hx-target" => "#output_card_card_body", "hx-swap" => "innerHtml"]) ?>

    <?= Edge::render('forms-button', ["id" => "Get_prices", "label" => "Update Prices", "hx-post" => "" . APP_ROOT . "/api/system/system/update_prices", "hx-target" => "#output_card_card_body", "hx-swap" => "innerHtml"]) ?>

    <?= Edge::render('forms-button', ["label" => "Get Subscriptions for api", "hx-post" => "" . APP_ROOT . "/api/system/system/subscriptions_get_from_api", "hx-target" => "#output_card_card_body", "hx-swap" => "innerHtml"]) ?>
    <?= Edge::render('forms-button', ["label" => "Get Quote JSON from api", "hx-post" => "" . APP_ROOT . "/api/system/system/quotes_get_from_api", "hx-target" => "#output_card_card_body", "hx-swap" => "innerHtml"]) ?>

    <?= Edge::render('forms-button', ["label" => "Get Promos", "hx-target" => "#output_card_body", "hx-swap" => "innerHtml", "hx-post" => "" . APP_ROOT . "/api/system/system/autodesk/get_promos_from_api", 'hx-vals' => "" . json_encode(["action" => "autodesk_get_promos_csv"]) . ""]) ?>

    <?php $component_props = array_merge([
'endpoint' => '',
'extra_attributes' => ''
], ["id" => "autodesk_search_customers_form", "class" => "form-control", "hx-post" => "" . APP_ROOT . "/api/system/system/api_h.php", "hx-swap" => "innerHtml", "hx-target" => "#output_card_body"]); extract($component_props, EXTR_SKIP); ?>


<form :hx-post="<?= $endpoint ?>" <?= $extra_attributes ?>>
    

    <h3>Search Customers</h3>
    <?= Edge::render('forms-input', ["type" => "text", "name" => "name", "id" => "name_input", "placeholder" => "name", "class" => "form-control"]) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "countryCode", "id" => "countryCode_input", "placeholder" => "countryCode", "class" => "form-control"]) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "contactEmail", "id" => "email_input", "placeholder" => "Email", "class" => "form-control"]) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "subscriptionId", "id" => "subscriptionId_input", "placeholder" => "subscriptionId", "class" => "form-control"]) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "endpoint", "id" => "endpoint_input", "placeholder" => "endpoint", "class" => "form-control"]) ?>
    <?= Edge::render('forms-button', ["label" => "Submit", "class" => "btn btn-primary"]) ?>
    
</form><br><br>


    <h3>Get opportunity</h3>

    <?php $component_props = array_merge([
'endpoint' => '',
'extra_attributes' => ''
], ["id" => "autodesk_get_opportunity_form", "hx-post" => "" . APP_ROOT . "/api_h.php", "hx-swap" => "innerHtml", "hx-target" => "#output_card_body"]); extract($component_props, EXTR_SKIP); ?>


<form :hx-post="<?= $endpoint ?>" <?= $extra_attributes ?>>
    
    <?= Edge::render('forms-input', ["type" => "text", "name" => "endCustomerCsn", "id" => "endCustomerCsn_input", "placeholder" => "endCustomerCsn", "class" => "form-control"]) ?>

    <?= Edge::render('forms-input', ["type" => "text", "name" => "opportunityNumber", "id" => "opportunityNumber_input", "placeholder" => "opportunityNumber", "class" => "form-control"]) ?>

    <?= Edge::render('forms-button', ["label" => "Submit", "class" => "btn btn-primary"]) ?>

    
</form><br><br>
    <?php $component_props = array_merge([
'endpoint' => '',
'extra_attributes' => ''
], ["id" => "autodesk_raw_api_call_form", "hx-post" => "" . APP_ROOT . "/api/system/system/autodesk_send_raw_api_call", "hx-swap" => "innerHtml", "hx-target" => "#output_card_card_body"]); extract($component_props, EXTR_SKIP); ?>


<form :hx-post="<?= $endpoint ?>" <?= $extra_attributes ?>>
    
    <?= Edge::render('forms-input', ["type" => "text", "name" => "requestType", "id" => "requestType_input", "placeholder" => "requestType (POST, GET)", "class" => "form-control"]) ?>
    <?= Edge::render('forms-input', ["type" => "text", "name" => "endpoint", "id" => "endpoint_input", "placeholder" => "endpoint", "class" => "form-control"]) ?>
    <?= Edge::render('forms-input', ["type" => "text", "name" => "downloadFilepath", "id" => "downloadFilepath_input", "placeholder" => "downloadFilepath", "class" => "form-control"]) ?>

    <?= Edge::render('forms-textarea', ["name" => "params", "id" => "params_textarea", "placeholder" => "params", "label" => "params (php assoc array)", "class" => "form-control"]) ?>

    <?= Edge::render('forms-textarea', ["name" => "json", "id" => "json", "placeholder" => "json here", "label" => "input (json)", "class" => "form-control"]) ?>

    <button type="submit">Send Raw</button>
    
</form><br><br>


    <?php $component_props = array_merge([
    'title' => 'card',
    'description' => 'A layout card',
    'content' => '',
    'tag_content' => '',  // This will contain the wrapped content
    'padding' => '5',
    'label' => '',
    'id' => 'card_' . rand(0,10000),
    'class_suffix' => '',
    'internal_class_suffix' => '',
    'scrolling' => false,
    'collapsible' => false,
    'collapsed' => false,
], ["id" => "output_card", "label" => "Output"]); extract($component_props, EXTR_SKIP); ?>

<?php if($label != '' && $collapsible): ?>
    <?php print_rr('start of card' ,''); ?>
<div x-data="{ open_<?= $id ?>: <?= (($collapsed || $collapsed == 'true') && $collapsed != 'false') ? 'false' : 'true' ?> }" class="m-1 h-full flex flex-col rounded-lg ">
    <div Class="h-full top-0 overflow-<?= $scrolling ? 'auto' : 'hidden' ?> bg-white shadow <?= $class_suffix ?>" >
         <button @click='open_<?= $id ?> = !open_<?= $id ?>'
                    class='w-full px-4 py-2 text-left text-lg font-semibold text-gray-700 transition-colors duration-200'
                    :class='open_<?= $id ?> ? "bg-gray-100 hover:bg-gray-200" : "bg-white hover:bg-gray-50"'>
                <?= $label ?>
                <span x-show='!open_<?= $id ?>' class='ml-2'>+</span>
                <span x-show='open_<?= $id ?>' class='ml-2'>-</span>
            </button>
    </div>
    <div <?php if($collapsible): ?> x-show='open_<?= $id ?>' <?php endif ?> class='h-min bg-white shadow px-4 py-2 gap-4 border-t border-gray-200 <?= $internal_class_suffix ?>' :class="{ 'flex-grow': open_<?= $id ?> }">
<?php else: ?>
<div class="m-1 top-0 overflow-<?= $scrolling ? 'auto' : 'hidden' ?> rounded-lg bg-white shadow <?= $class_suffix ?>">
        <?php if($label != ''): ?>
            <div class="px-4 py-2 text-left text-lg font-semibold text-gray-700 bg-gray-100 hover:bg-gray-300 focus:outline-none">
                <?= $label ?>
            </div>
        <?php endif ?>
    <div class='h-min px-4 py-2 gap-4 border-t border-gray-200 bg-white shadow <?= $internal_class_suffix ?>'>
<?php endif ?>
            
            
      <div id="output_card_card_body"></div>
    
    </div>
</div>
            <?php print_rr('end of card' ,''); ?>

  </div>
</div>
