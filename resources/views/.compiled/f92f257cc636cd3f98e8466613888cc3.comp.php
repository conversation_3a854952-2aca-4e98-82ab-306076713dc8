<?php namespace edgeTemplate\view_f92f257cc636cd3f98e8466613888cc3;use edge\Edge;?><?php extract(['edge_manifest' => array (
)]);; ?>    
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="md:flex md:items-center md:justify-between">
                    <div class="flex-1 min-w-0">
                        <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                            <?= htmlspecialchars($page_title) ?>
                        </h1>
                        <p class="mt-1 text-sm text-gray-500">
                            <?= htmlspecialchars($page_description) ?>
                        </p>
                    </div>
                    <div class="mt-4 flex md:mt-0 md:ml-4">
                        <button type="button" 
                                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                onclick="window.location.href='<?= APP_ROOT ?>/unified/settings'">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Settings
                        </button>
                     </div>
                </div>
                <div id="sync-status" class="mt-2"></div>
            </div>
        </div>
    </div>

    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <div hx-get="<?= APP_ROOT ?>/api/unified/dashboard_stats" hx-trigger="load">
            <?= Edge::render('dashboard-stats', ['show_loading' => true]) ?>
        </div>

        
        <?= Edge::render('unified-subscriptions-table', []) ?>
    </div>
<?php
     //
    //<div x-show="showModal" 
         //x-transition:enter="ease-out duration-300"
         //x-transition:enter-start="opacity-0"
         //x-transition:enter-end="opacity-100"
         //x-transition:leave="ease-in duration-200"
         //x-transition:leave-start="opacity-100"
         //x-transition:leave-end="opacity-0"
         //class="fixed inset-0 z-50 overflow-y-auto"
         //style="display: none;">
        //<div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            //<div x-show="showModal"
                 //x-transition:enter="ease-out duration-300"
                 //x-transition:enter-start="opacity-0"
                 //x-transition:enter-end="opacity-100"
                 //x-transition:leave="ease-in duration-200"
                 //x-transition:leave-start="opacity-100"
                 //x-transition:leave-end="opacity-0"
                 //class="fixed inset-0 transition-opacity">
                //<div class="absolute inset-0 bg-gray-500 opacity-75" @click="showModal = false"></div>
            //</div>

            //<span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

            //<div x-show="showModal"
                 //x-transition:enter="ease-out duration-300"
                 //x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 //x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 //x-transition:leave="ease-in duration-200"
                 //x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 //x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 //class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full sm:p-6">
                
                //<div class="absolute top-0 right-0 pt-4 pr-4">
                    //<button type="button" 
                            //@click="showModal = false"
                            //class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        //<span class="sr-only">Close</span>
                        //<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            //<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        //</svg>
                    //</button>
                //</div>

                //<div id="modal_body">
                     //
                //</div>
            //</div>
        //</div>
    //</div>
?>


