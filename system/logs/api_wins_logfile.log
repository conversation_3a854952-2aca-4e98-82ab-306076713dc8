[api_wins] [2025-08-10 13:25:25] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:25] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(12) "api\unified\"\n  ["function_call"]: string(39) "api\unified\unified_subscriptions_table"\n  ["path_parts"]: array(1) {\n    [0]: string(7) "unified"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resourc...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resourc...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:25] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:25] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(12) "api\unified\"\n  ["function_call"]: string(27) "api\unified\dashboard_stats"\n  ["path_parts"]: array(1) {\n    [0]: string(7) "unified"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resourc...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resourc...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:31] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:31] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:32] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(28) "api\data_sources\create_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:40] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(40) "api\data_sources\update_data_source_type"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:43] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(37) "api\data_sources\add_wildcard_pattern"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:47] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(38) "api\data_sources\update_mapping_method"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-10 13:25:54] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(36) "api\data_sources\preview_merged_data"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
