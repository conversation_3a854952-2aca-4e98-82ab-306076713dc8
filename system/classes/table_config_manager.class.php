<?php
namespace system;

use system\database;
use system\Schema;

/**
 * Table Configuration Manager
 * 
 * Manages persistent storage and retrieval of data table configurations
 * for performance optimization, similar to autodesk subscription table approach
 */
class table_config_manager {
    public static $log_target = "table_config_manager";
    
    private const CONFIG_TABLE = 'autobooks_table_configs';

    /**
     * Ensure the configuration table exists
     */
    public static function ensure_config_table_exists(): bool {
        try {
            if (!database::tableExists(self::CONFIG_TABLE)) {
                Schema::create(self::CONFIG_TABLE, function($table) {
                    $table->increments('id');
                    $table->string('table_name', 255);
                    $table->string('route_key', 255);
                    $table->json('table_schema');
                    $table->json('table_config');
                    $table->json('column_mappings');
                    $table->text('description')->nullable();
                    $table->string('data_source', 100)->default('csv'); // csv, api, manual
                    $table->boolean('is_active')->default(true);
                    $table->timestamps();
                    
                    // Add indexes for performance
                    $table->unique('table_name');
                    $table->index('route_key');
                });
                
                tcs_log("Created table configuration storage table: " . self::CONFIG_TABLE, self::$log_target);
                return true;
            }
            return true;
        } catch (\Exception $e) {
            tcs_log("Failed to create config table: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }

    /**
     * Store table configuration
     *
     * @param string $table_name Database table name
     * @param string $route_key Route identifier
     * @param array $schema Enhanced schema definition
     * @param array $config Generated table configuration
     * @param array $options Additional options
     * @return array Storage result
     */
    public static function store_table_config(string $table_name, string $route_key, array $schema, array $config, array $options = []): array {
        try {
            tcs_log("store_table_config called for table: {$table_name}, route: {$route_key}", self::$log_target);

            // Store in both systems for compatibility
            $legacy_result = self::store_legacy_config($table_name, $route_key, $schema, $config, $options);
            $unified_result = self::store_unified_config($table_name, $route_key, $schema, $config, $options);

            return $unified_result;

            // Prepare column mappings for easy reference
            $column_mappings = [
                'csv_to_db' => $schema['mapping']['main']['columns'] ?? [],
                'data_types' => $schema['mapping']['main']['data_types'] ?? [],
                'column_labels' => []
            ];

            // Extract column labels from config
            foreach ($config['columns'] as $column) {
                $column_mappings['column_labels'][$column['field']] = $column['label'];
            }

            $config_data = [
                'table_name' => $table_name,
                'route_key' => $route_key,
                'table_schema' => json_encode($schema),
                'table_config' => json_encode($config),
                'column_mappings' => json_encode($column_mappings),
                'description' => $options['description'] ?? "Auto-generated configuration for {$table_name}",
                'data_source' => $options['data_source'] ?? 'csv',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Check if configuration already exists
            $existing = database::table(self::CONFIG_TABLE)
                ->where('table_name', $table_name)
                ->first();

            if ($existing) {
                // Update existing configuration
                $config_data['updated_at'] = date('Y-m-d H:i:s');
                database::table(self::CONFIG_TABLE)
                    ->where('table_name', $table_name)
                    ->update($config_data);
                
                tcs_log("Updated table configuration for: {$table_name}", self::$log_target);
                $action = 'updated';
            } else {
                // Insert new configuration
                database::table(self::CONFIG_TABLE)->insert($config_data);
                tcs_log("Stored new table configuration for: {$table_name}", self::$log_target);
                $action = 'created';
            }

            return [
                'success' => true,
                'message' => "Table configuration {$action} successfully",
                'table_name' => $table_name,
                'route_key' => $route_key,
                'action' => $action
            ];

        } catch (\Exception $e) {
            tcs_log("Error storing table config: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to store table configuration: " . $e->getMessage()];
        }
    }

    /**
     * Store configuration in legacy autobooks_table_configs table
     */
    private static function store_legacy_config(string $table_name, string $route_key, array $schema, array $config, array $options = []): array {
        try {
            if (!self::ensure_config_table_exists()) {
                return ['error' => 'Failed to ensure config table exists'];
            }

            // Generate column mappings from schema
            $column_mappings = [];
            if (isset($schema['columns'])) {
                foreach ($schema['columns'] as $column_name => $column_def) {
                    $column_mappings[$column_name] = [
                        'type' => $column_def['type'] ?? 'string',
                        'label' => ucfirst(str_replace('_', ' ', $column_name)),
                        'nullable' => $column_def['nullable'] ?? true
                    ];
                }
            }

            $config_data = [
                'table_name' => $table_name,
                'route_key' => $route_key,
                'table_schema' => json_encode($schema),
                'table_config' => json_encode($config),
                'column_mappings' => json_encode($column_mappings),
                'description' => $options['description'] ?? "Auto-generated configuration for {$table_name}",
                'data_source' => $options['data_source'] ?? 'csv',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Check if configuration already exists
            $existing = database::table(self::CONFIG_TABLE)
                ->where('table_name', $table_name)
                ->first();

            if ($existing) {
                $config_data['updated_at'] = date('Y-m-d H:i:s');
                database::table(self::CONFIG_TABLE)
                    ->where('table_name', $table_name)
                    ->update($config_data);
                $action = 'updated';
            } else {
                database::table(self::CONFIG_TABLE)->insert($config_data);
                $action = 'created';
            }

            tcs_log("Legacy config {$action} for: {$table_name}", self::$log_target);
            return ['success' => true, 'action' => $action];

        } catch (\Exception $e) {
            tcs_log("Error storing legacy config: " . $e->getMessage(), self::$log_target);
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Store configuration in unified data_table_storage system
     */
    private static function store_unified_config(string $table_name, string $route_key, array $schema, array $config, array $options = []): array {
        try {
            // Convert config to data_table_storage format
            $unified_config = self::convert_to_unified_format($table_name, $schema, $config, $options);

            // Store using data_table_storage system (pass null for data_source_id since we don't have a specific ID)
            $success = \data_table_storage::save_configuration($table_name, $unified_config, null, null);

            if ($success) {
                tcs_log("Unified config stored for: {$table_name}", self::$log_target);
                return [
                    'success' => true,
                    'message' => "Table configuration stored successfully",
                    'table_name' => $table_name,
                    'route_key' => $route_key
                ];
            } else {
                return ['error' => 'Failed to store unified configuration'];
            }

        } catch (\Exception $e) {
            tcs_log("Error storing unified config: " . $e->getMessage(), self::$log_target);
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Convert legacy config format to unified data_table_storage format
     */
    private static function convert_to_unified_format(string $table_name, array $schema, array $config, array $options = []): array {
        $columns = [];
        $hidden_columns = [];
        $available_fields = [];

        // Process schema columns
        if (isset($schema['columns'])) {
            $column_index = 0;
            foreach ($schema['columns'] as $column_name => $column_def) {
                $column_id = 'col_' . $column_index . '_' . md5($column_name);

                // Determine if column should be visible by default
                $is_visible = !in_array($column_name, $options['hidden_columns'] ?? []);

                // Create column structure
                $column_structure = [
                    'id' => $column_id,
                    'label' => self::format_column_label($column_name),
                    'field' => $column_name,
                    'fields' => [$column_name],
                    'filter' => self::should_enable_filter_for_column($column_def),
                    'visible' => $is_visible
                ];

                $columns[] = $column_structure;

                // Add to available fields (all columns are available)
                $available_fields[] = $column_name;

                // Track hidden columns
                if (!$is_visible) {
                    $hidden_columns[] = $column_id;
                }

                $column_index++;
            }
        }

        return [
            'structure' => $columns,
            'hidden' => $hidden_columns,
            'available_fields' => $available_fields,
            'table_schema' => $schema,
            'data_source' => $options['data_source'] ?? 'csv',
            'route_key' => $route_key, // Store route_key in the configuration
            'description' => $options['description'] ?? '',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Determine if a column should have filtering enabled based on its definition
     */
    private static function should_enable_filter_for_column(array $column_def): bool {
        $type = strtolower($column_def['type'] ?? '');

        // Enable filters for text and enum types
        return (strpos($type, 'varchar') !== false ||
                strpos($type, 'text') !== false ||
                strpos($type, 'enum') !== false ||
                strpos($type, 'char') !== false);
    }

    /**
     * Format column name into a user-friendly label
     */
    private static function format_column_label(string $column_name): string {
        // Remove common prefixes/suffixes
        $label = preg_replace('/^(autobooks_|tbl_|col_)/', '', $column_name);
        $label = preg_replace('/(_id|_key|_ref)$/', '', $label);

        // Convert underscores to spaces and capitalize
        $label = str_replace('_', ' ', $label);
        $label = ucwords($label);

        // Handle common abbreviations
        $replacements = [
            'Id' => 'ID',
            'Url' => 'URL',
            'Api' => 'API',
            'Csv' => 'CSV',
            'Json' => 'JSON',
            'Xml' => 'XML',
            'Html' => 'HTML',
            'Sql' => 'SQL',
            'Uuid' => 'UUID'
        ];

        foreach ($replacements as $search => $replace) {
            $label = str_replace($search, $replace, $label);
        }

        return $label;
    }

    /**
     * Retrieve stored table configuration
     *
     * @param string $table_name Database table name
     * @return array|null Configuration data or null if not found
     */
    public static function get_table_config(string $table_name): ?array {
        try {
            if (!database::tableExists(self::CONFIG_TABLE)) {
                return null;
            }

            $config_row = database::table(self::CONFIG_TABLE)
                ->where('table_name', $table_name)
                ->where('is_active', true)
                ->first();

            if (!$config_row) {
                return null;
            }

            return [
                'id' => $config_row['id'],
                'table_name' => $config_row['table_name'],
                'route_key' => $config_row['route_key'],
                'schema' => json_decode($config_row['table_schema'], true),
                'config' => json_decode($config_row['table_config'], true),
                'column_mappings' => json_decode($config_row['column_mappings'], true),
                'description' => $config_row['description'],
                'data_source' => $config_row['data_source'],
                'created_at' => $config_row['created_at'],
                'updated_at' => $config_row['updated_at']
            ];

        } catch (\Exception $e) {
            tcs_log("Error retrieving table config: " . $e->getMessage(), self::$log_target);
            return null;
        }
    }

    /**
     * Update table configuration with new data and criteria
     *
     * @param string $table_name Database table name
     * @param array $criteria Query criteria for data retrieval
     * @param array $options Display options
     * @return array Updated configuration or error
     */
    public static function get_updated_config_with_data(string $table_name, array $criteria = [], array $options = []): array {
        try {
            // Check if intelligent naming is requested and force regeneration
            $use_intelligent_naming = $options['use_intelligent_naming'] ?? true;
            $force_regenerate = $options['force_regenerate'] ?? false;

            // Get stored configuration
            $stored_config = self::get_table_config($table_name);
            if (!$stored_config || $force_regenerate) {
                return ['error' => "No stored configuration found for table: {$table_name}"];
            }

            $base_config = $stored_config['config'];

            // If intelligent naming is requested, update column labels
            if ($use_intelligent_naming && class_exists('column_analyzer')) {
                $base_config = self::update_config_with_intelligent_naming($table_name, $base_config);
            }

            // Retrieve fresh data based on criteria
            $data_result = self::retrieve_table_data($table_name, $criteria);
            if (isset($data_result['error'])) {
                return $data_result;
            }

            // Generate available_fields from stored configuration
            $available_fields = [];
            if (isset($stored_config['schema']['columns'])) {
                $available_fields = array_keys($stored_config['schema']['columns']);
            } elseif (isset($base_config['columns'])) {
                // Fallback: extract from column definitions
                foreach ($base_config['columns'] as $column) {
                    if (isset($column['field'])) {
                        if (is_array($column['field'])) {
                            $available_fields = array_merge($available_fields, $column['field']);
                        } else {
                            $available_fields[] = $column['field'];
                        }
                    }
                }
                $available_fields = array_unique($available_fields);
            }

            // Update configuration with fresh data and criteria
            $updated_config = array_merge($base_config, [
                'items' => $data_result['data'],
                'available_fields' => $available_fields,
                'items_per_page' => $options['items_per_page'] ?? $criteria['limit'] ?? 30,
                'current_page_num' => $options['current_page_num'] ?? (($criteria['offset'] ?? 0) / ($criteria['limit'] ?? 30)) + 1,
                'sort_column' => $criteria['order_by'] ?? '',
                'sort_direction' => $criteria['order_direction'] ?? 'asc',
                'callback' => $options['callback'] ?? 'data_table_filter',
                'just_body' => $options['just_body'] ?? false,
                'just_rows' => $options['just_rows'] ?? false
            ]);

            return ['success' => true, 'config' => $updated_config, 'stored_config' => $stored_config];

        } catch (\Exception $e) {
            tcs_log("Error updating config with data: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to update configuration: " . $e->getMessage()];
        }
    }

    /**
     * Retrieve data from database table (similar to data_table_generator)
     */
    private static function retrieve_table_data(string $table_name, array $criteria = []): array {
        try {
            $query = database::table($table_name);

            // Apply WHERE conditions
            if (isset($criteria['where']) && is_array($criteria['where'])) {
                foreach ($criteria['where'] as $column => $condition) {
                    if (is_array($condition) && count($condition) >= 2) {
                        $operator = $condition[0];
                        $value = $condition[1];
                        $query->where($column, $operator, $value);
                    } else {
                        $query->where($column, $condition);
                    }
                }
            }

            // Apply search
            if (isset($criteria['search']) && !empty($criteria['search'])) {
                $search_term = $criteria['search'];
                $search_columns = $criteria['search_columns'] ?? [];
                
                if (!empty($search_columns)) {
                    $query->where(function($q) use ($search_columns, $search_term) {
                        foreach ($search_columns as $column) {
                            $q->orWhere($column, 'LIKE', "%{$search_term}%");
                        }
                    });
                }
            }

            // Apply ordering
            if (isset($criteria['order_by'])) {
                $direction = $criteria['order_direction'] ?? 'asc';
                $query->orderBy($criteria['order_by'], $direction);
            }

            // Apply pagination
            if (isset($criteria['limit'])) {
                $query->limit($criteria['limit']);
                
                if (isset($criteria['offset'])) {
                    $query->offset($criteria['offset']);
                }
            }

            $data = $query->get();

            return [
                'success' => true,
                'data' => $data,
                'count' => count($data)
            ];

        } catch (\Exception $e) {
            tcs_log("Error retrieving table data: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to retrieve data: " . $e->getMessage()];
        }
    }

    /**
     * List all stored table configurations
     *
     * @return array List of configurations
     */
    public static function list_all_configs(): array {
        try {
            if (!database::tableExists(self::CONFIG_TABLE)) {
                return [];
            }

            $configs = database::table(self::CONFIG_TABLE)
                ->where('is_active', true)
                ->orderBy('table_name')
                ->get();

            return array_map(function($config) {
                return [
                    'table_name' => $config['table_name'],
                    'route_key' => $config['route_key'],
                    'description' => $config['description'],
                    'data_source' => $config['data_source'],
                    'created_at' => $config['created_at'],
                    'updated_at' => $config['updated_at']
                ];
            }, $configs);

        } catch (\Exception $e) {
            tcs_log("Error listing configs: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }

    /**
     * Delete table configuration
     *
     * @param string $table_name Database table name
     * @return bool Success status
     */
    public static function delete_table_config(string $table_name): bool {
        try {
            if (!database::tableExists(self::CONFIG_TABLE)) {
                return true; // Nothing to delete
            }

            database::table(self::CONFIG_TABLE)
                ->where('table_name', $table_name)
                ->update(['is_active' => false, 'updated_at' => date('Y-m-d H:i:s')]);

            tcs_log("Deactivated table configuration for: {$table_name}", self::$log_target);
            return true;

        } catch (\Exception $e) {
            tcs_log("Error deleting table config: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }

    /**
     * Update configuration with intelligent column naming
     *
     * @param string $table_name Database table name
     * @param array $config Existing configuration
     * @return array Updated configuration with intelligent column names
     */
    private static function update_config_with_intelligent_naming(string $table_name, array $config): array {
        try {
            if (!isset($config['columns']) || !is_array($config['columns'])) {
                return $config;
            }

            // Get all column names for context analysis
            $all_column_names = [];
            foreach ($config['columns'] as $column) {
                if (isset($column['field'])) {
                    $all_column_names[] = $column['field'];
                }
            }

            // Update each column with intelligent naming
            foreach ($config['columns'] as &$column) {
                if (!isset($column['field'])) {
                    continue;
                }

                $column_name = $column['field'];

                // Skip if already has a custom label that's different from auto-generated
                $auto_label = self::format_column_label($column_name);
                if (isset($column['label']) && $column['label'] !== $auto_label) {
                    continue; // Keep custom labels
                }

                // Analyze the column
                $analysis = \column_analyzer::analyze_column($table_name, $column_name, $all_column_names);

                // If we have a high confidence suggestion, use it
                if ($analysis['confidence'] >= 50 && $analysis['suggested_name'] !== $column_name) {
                    $suggested_name = $analysis['suggested_name'];
                    $new_label = self::format_column_label($suggested_name);

                    // Update the column label
                    $column['label'] = $new_label;

                    // Log the intelligent naming for debugging
                    tcs_log("Intelligent naming applied: {$column_name} -> {$suggested_name} (confidence: {$analysis['confidence']}%)", 'table_config_manager');
                }
            }

            return $config;

        } catch (\Exception $e) {
            tcs_log("Error updating config with intelligent naming: " . $e->getMessage(), self::$log_target);
            return $config; // Return original config on error
        }
    }


}
