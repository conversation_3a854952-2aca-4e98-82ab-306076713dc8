<?php

namespace system;

use system\unified_field_definitions;

/**
 * Unified Field Mapper
 *
 * Provides intelligent field mapping and normalization for CSV imports and subscription matching.
 * Now uses unified_field_definitions for centralized field management.
 */
class unified_field_mapper {
    
    private static $log_target = 'unified_field_mapper';
    
    /**
     * Get field mappings from unified field definitions
     */
    private static function get_field_mappings(): array {
        $all_fields = unified_field_definitions::get_all_fields();
        $mappings = [];

        foreach ($all_fields as $field_name => $definition) {
            $mappings[$field_name] = [
                'patterns' => $definition['patterns'] ?? [],
                'normalized_fields' => $definition['normalized_fields'] ?? []
            ];
        }

        return $mappings;
    }
    
    /**
     * Normalize a CSV entry using the unified field mapping
     * 
     * @param array $entry Original CSV row data
     * @param string $source_table Table name for context
     * @return array Normalized entry with standardized field names
     */
    public static function normalize_entry(array $entry, string $source_table = ''): array {
        $normalized = [
            'id' => $entry['id'] ?? uniqid(),
            'source_table' => $source_table,
            'data_source' => 'csv_table'
        ];
        
        tcs_log("Normalizing entry from {$source_table} with " . count($entry) . " fields", self::$log_target);
        
        // Apply field mappings
        $field_mappings = self::get_field_mappings();
        foreach ($entry as $key => $value) {
            $lower_key = strtolower(trim($key));

            // Find matching field mapping
            foreach ($field_mappings as $category => $mapping) {
                if (in_array($lower_key, $mapping['patterns'])) {
                    // Map to all normalized field names for this category
                    foreach ($mapping['normalized_fields'] as $normalized_field) {
                        $normalized[$normalized_field] = $value;
                    }
                    tcs_log("Mapped {$key} -> {$category} (" . implode(', ', $mapping['normalized_fields']) . ")", self::$log_target);
                    break;
                }
            }

            // Always keep original field name as well
            $normalized[$key] = $value;
        }
        
        // Apply post-processing
        $normalized = self::apply_post_processing($normalized);
        
        return $normalized;
    }
    
    /**
     * Apply post-processing logic (date parsing, status calculation, etc.)
     */
    private static function apply_post_processing(array $normalized): array {
        // Calculate expiration status and days remaining
        if (!empty($normalized['end_date']) || !empty($normalized['subs_endDate'])) {
            $end_date = $normalized['end_date'] ?? $normalized['subs_endDate'];
            
            $end_timestamp = self::parse_date($end_date);
            if ($end_timestamp) {
                $now = time();
                $days_diff = ($end_timestamp - $now) / (60 * 60 * 24);
                $normalized['subs_enddatediff'] = round($days_diff);
                
                // Override status if expired
                if ($days_diff < 0) {
                    $normalized['subs_status'] = 'EXPIRED';
                    $normalized['status'] = 'EXPIRED';
                } elseif ($days_diff <= 30) {
                    // Keep original status but note it's expiring soon
                    if (empty($normalized['subs_status']) || $normalized['subs_status'] === 'Unknown') {
                        $normalized['subs_status'] = 'EXPIRING';
                    }
                }
                
                tcs_log("Date processing: {$end_date} -> {$days_diff} days remaining", self::$log_target);
            }
        }
        
        return $normalized;
    }
    
    /**
     * Parse date from various formats
     */
    private static function parse_date(string $date_string): ?int {
        if (empty($date_string)) return null;
        
        $formats = ['Y-m-d', 'd/m/Y', 'm/d/Y', 'Y-m-d H:i:s', 'd-m-Y', 'm-d-Y'];
        
        foreach ($formats as $format) {
            $date_obj = \DateTime::createFromFormat($format, $date_string);
            if ($date_obj !== false) {
                return $date_obj->getTimestamp();
            }
        }
        
        // Try strtotime as fallback
        $timestamp = strtotime($date_string);
        return $timestamp !== false ? $timestamp : null;
    }
    
    /**
     * Check if a table contains subscription-related data based on field analysis
     */
    public static function contains_subscription_data(string $table_name, array $available_fields = []): bool {
        if (empty($available_fields)) {
            // Get fields from table if not provided
            try {
                $columns_query = "SHOW COLUMNS FROM `{$table_name}`";
                $columns = tcs_db_query($columns_query) ?: [];
                $available_fields = array_column($columns, 'Field');
            } catch (\Exception $e) {
                return false;
            }
        }
        
        $field_names = array_map('strtolower', $available_fields);
        
        // Look for subscription-related keywords
        $subscription_indicators = [
            'subscription', 'license', 'agreement', 'contract', 'product', 'software',
            'email', 'company', 'customer', 'client', 'end_date', 'expiry', 'quantity'
        ];
        
        $matches = 0;
        foreach ($subscription_indicators as $indicator) {
            foreach ($field_names as $field_name) {
                if (strpos($field_name, $indicator) !== false) {
                    $matches++;
                    break;
                }
            }
        }
        
        // Consider it subscription data if it has 3+ relevant fields
        $has_subscription_data = $matches >= 3;
        
        tcs_log("Table {$table_name} subscription analysis: {$matches} matches, result: " . ($has_subscription_data ? 'YES' : 'NO'), self::$log_target);
        
        return $has_subscription_data;
    }
    
    /**
     * Get field mapping suggestions for a given set of column names
     */
    public static function suggest_field_mappings(array $column_names): array {
        $suggestions = [];
        
        foreach ($column_names as $column) {
            $lower_column = strtolower(trim($column));
            
            foreach (self::$field_mappings as $category => $mapping) {
                if (in_array($lower_column, $mapping['patterns'])) {
                    $suggestions[$column] = [
                        'category' => $category,
                        'normalized_fields' => $mapping['normalized_fields'],
                        'confidence' => 100 // Exact match
                    ];
                    break;
                }
            }
            
            // If no exact match, try partial matching with better logic
            if (!isset($suggestions[$column])) {
                $best_match = null;
                $best_confidence = 0;

                foreach (self::$field_mappings as $category => $mapping) {
                    foreach ($mapping['patterns'] as $pattern) {
                        $confidence = self::calculate_pattern_confidence($lower_column, $pattern);

                        if ($confidence > $best_confidence && $confidence >= 50) {
                            $best_match = [
                                'category' => $category,
                                'normalized_fields' => $mapping['normalized_fields'],
                                'confidence' => $confidence
                            ];
                            $best_confidence = $confidence;
                        }
                    }
                }

                if ($best_match) {
                    $suggestions[$column] = $best_match;
                }
            }
        }
        
        return $suggestions;
    }

    /**
     * Calculate confidence score for pattern matching
     *
     * @param string $column_name The column name to match
     * @param string $pattern The pattern to match against
     * @return int Confidence score (0-100)
     */
    private static function calculate_pattern_confidence(string $column_name, string $pattern): int {
        // Exact match gets 100%
        if ($column_name === $pattern) {
            return 100;
        }

        // Check if column contains the pattern as a whole word
        if (preg_match('/\b' . preg_quote($pattern, '/') . '\b/', $column_name)) {
            return 90;
        }

        // Check if column ends with the pattern
        if (substr($column_name, -strlen($pattern)) === $pattern) {
            return 85;
        }

        // Check if column starts with the pattern
        if (substr($column_name, 0, strlen($pattern)) === $pattern) {
            return 80;
        }

        // Check if pattern is contained in column (but avoid short patterns that could cause false matches)
        if (strlen($pattern) >= 4 && strpos($column_name, $pattern) !== false) {
            // Calculate confidence based on how much of the column name the pattern represents
            $pattern_coverage = strlen($pattern) / strlen($column_name);
            return max(50, min(75, (int)(50 + ($pattern_coverage * 25))));
        }

        // Check if column is contained in pattern (for compound patterns)
        if (strlen($column_name) >= 4 && strpos($pattern, $column_name) !== false) {
            $column_coverage = strlen($column_name) / strlen($pattern);
            return max(50, min(70, (int)(50 + ($column_coverage * 20))));
        }

        // No meaningful match
        return 0;
    }
}
