<?php

namespace system;

use system\unified_field_definitions;

/**
 * Subscription Matching Rules Manager
 *
 * Manages configurable rules for subscription matching including:
 * - Field mapping rules
 * - Matching criteria and thresholds
 * - Priority and weighting
 * - Custom matching logic
 *
 * Now uses unified_field_definitions for centralized field management
 */
class subscription_matching_rules {
    
    private static $log_target = 'subscription_matching_rules';
    private static $rules_table = 'autobooks_subscription_matching_rules';
    
    /**
     * Default matching rules configuration
     */
    private static $default_rules = [
        'email_matching' => [
            'enabled' => true,
            'priority' => 1,
            'confidence_score' => 100,
            'field_patterns' => [
                'endcust_primary_admin_email',
                'end_customer_contact_email', 
                'subscription_contact_email',
                'email_address',
                'email',
                'contact_email',
                'admin_email',
                'customer_email'
            ],
            'case_sensitive' => false,
            'exact_match_only' => true
        ],
        
        'company_name_matching' => [
            'enabled' => true,
            'priority' => 2,
            'confidence_threshold' => 70,
            'field_patterns' => [
                'endcust_name',
                'end_customer_name',
                'company_name',
                'customer_name',
                'organization_name',
                'business_name',
                'sold_to_name',
                'vendor_name'
            ],
            'fuzzy_matching' => true,
            'similarity_threshold' => 70,
            'case_sensitive' => false,
            'preprocessing' => [
                'remove_common_words' => ['ltd', 'limited', 'inc', 'corp', 'corporation', 'llc', 'plc'],
                'normalize_spaces' => true,
                'remove_punctuation' => true
            ]
        ],
        
        'subscription_reference_matching' => [
            'enabled' => true,
            'priority' => 0, // Highest priority
            'confidence_score' => 100,
            'field_patterns' => [
                'subscription_reference',
                'subscription_id',
                'agreement_number',
                'contract_number',
                'license_key',
                'serial_number',
                'reference_number'
            ],
            'case_sensitive' => false,
            'exact_match_only' => true
        ],
        
        'contact_name_matching' => [
            'enabled' => false, // Disabled by default (less reliable)
            'priority' => 3,
            'confidence_threshold' => 80,
            'field_patterns' => [
                'contact_name',
                'end_customer_contact_name',
                'subscription_contact_name',
                'admin_name',
                'primary_contact'
            ],
            'fuzzy_matching' => true,
            'similarity_threshold' => 80,
            'case_sensitive' => false
        ],
        
        'phone_matching' => [
            'enabled' => false, // Disabled by default
            'priority' => 4,
            'confidence_score' => 90,
            'field_patterns' => [
                'phone',
                'phone_number',
                'contact_phone',
                'end_customer_contact_phone',
                'business_phone'
            ],
            'normalize_phone' => true,
            'exact_match_only' => true
        ]
    ];
    
    /**
     * Get all matching rules (from database or defaults)
     */
    public static function get_rules(): array {
        try {
            if (!self::ensure_rules_table_exists()) {
                return self::$default_rules;
            }
            
            $stored_rules = database::table(self::$rules_table)
                ->where('is_active', 1)
                ->orderBy('priority', 'asc')
                ->get();
            
            if (empty($stored_rules)) {
                // Initialize with defaults
                self::initialize_default_rules();
                return self::$default_rules;
            }
            
            // Convert stored rules to array format
            $rules = [];
            foreach ($stored_rules as $rule) {
                $config = json_decode($rule['configuration'], true) ?: [];
                $rules[$rule['rule_name']] = array_merge($config, [
                    'id' => $rule['id'],
                    'enabled' => (bool)$rule['is_active'],
                    'priority' => $rule['priority']
                ]);
            }
            
            return $rules;
            
        } catch (\Exception $e) {
            tcs_log("Error loading rules: " . $e->getMessage(), self::$log_target);
            return self::$default_rules;
        }
    }
    
    /**
     * Save matching rules to database
     */
    public static function save_rules(array $rules): bool {
        try {
            if (!self::ensure_rules_table_exists()) {
                return false;
            }
            
            foreach ($rules as $rule_name => $config) {
                $rule_data = [
                    'rule_name' => $rule_name,
                    'priority' => $config['priority'] ?? 99,
                    'is_active' => $config['enabled'] ?? true,
                    'configuration' => json_encode($config),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                // Check if rule exists
                $existing = database::table(self::$rules_table)
                    ->where('rule_name', $rule_name)
                    ->first();
                
                if ($existing) {
                    database::table(self::$rules_table)
                        ->where('rule_name', $rule_name)
                        ->update($rule_data);
                } else {
                    $rule_data['created_at'] = date('Y-m-d H:i:s');
                    database::table(self::$rules_table)->insert($rule_data);
                }
            }
            
            tcs_log("Successfully saved " . count($rules) . " matching rules", self::$log_target);
            return true;
            
        } catch (\Exception $e) {
            tcs_log("Error saving rules: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }
    
    /**
     * Get a specific rule by name
     */
    public static function get_rule(string $rule_name): ?array {
        $rules = self::get_rules();
        return $rules[$rule_name] ?? null;
    }
    
    /**
     * Update a specific rule
     */
    public static function update_rule(string $rule_name, array $config): bool {
        $rules = self::get_rules();
        $rules[$rule_name] = $config;
        return self::save_rules($rules);
    }
    
    /**
     * Enable/disable a rule
     */
    public static function toggle_rule(string $rule_name, bool $enabled): bool {
        $rules = self::get_rules();
        if (isset($rules[$rule_name])) {
            $rules[$rule_name]['enabled'] = $enabled;
            return self::save_rules($rules);
        }
        return false;
    }
    
    /**
     * Reset rules to defaults
     */
    public static function reset_to_defaults(): bool {
        try {
            if (self::ensure_rules_table_exists()) {
                // Clear existing rules
                database::table(self::$rules_table)->truncate();
            }
            
            return self::save_rules(self::$default_rules);
            
        } catch (\Exception $e) {
            tcs_log("Error resetting rules: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }
    
    /**
     * Get rule statistics and usage
     */
    public static function get_rule_statistics(): array {
        $rules = self::get_rules();
        $stats = [
            'total_rules' => count($rules),
            'enabled_rules' => 0,
            'disabled_rules' => 0,
            'rules_by_priority' => []
        ];
        
        foreach ($rules as $name => $rule) {
            if ($rule['enabled']) {
                $stats['enabled_rules']++;
            } else {
                $stats['disabled_rules']++;
            }
            
            $priority = $rule['priority'] ?? 99;
            $stats['rules_by_priority'][$priority] = $name;
        }
        
        ksort($stats['rules_by_priority']);
        
        return $stats;
    }
    
    /**
     * Validate rule configuration
     */
    public static function validate_rule(array $rule_config): array {
        $errors = [];
        
        // Required fields
        if (empty($rule_config['field_patterns'])) {
            $errors[] = "Field patterns are required";
        }
        
        if (!isset($rule_config['priority']) || !is_numeric($rule_config['priority'])) {
            $errors[] = "Valid priority number is required";
        }
        
        // Validate thresholds
        if (isset($rule_config['similarity_threshold'])) {
            $threshold = $rule_config['similarity_threshold'];
            if (!is_numeric($threshold) || $threshold < 0 || $threshold > 100) {
                $errors[] = "Similarity threshold must be between 0 and 100";
            }
        }
        
        if (isset($rule_config['confidence_threshold'])) {
            $threshold = $rule_config['confidence_threshold'];
            if (!is_numeric($threshold) || $threshold < 0 || $threshold > 100) {
                $errors[] = "Confidence threshold must be between 0 and 100";
            }
        }
        
        return $errors;
    }
    
    /**
     * Initialize default rules in database
     */
    private static function initialize_default_rules(): bool {
        return self::save_rules(self::$default_rules);
    }
    
    /**
     * Ensure the rules table exists
     */
    private static function ensure_rules_table_exists(): bool {
        try {
            if (database::tableExists(self::$rules_table)) {
                return true;
            }
            
            $sql = "CREATE TABLE `" . self::$rules_table . "` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `rule_name` varchar(100) NOT NULL,
                `priority` int(11) NOT NULL DEFAULT 99,
                `is_active` tinyint(1) NOT NULL DEFAULT 1,
                `configuration` longtext NOT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `rule_name` (`rule_name`),
                KEY `priority` (`priority`),
                KEY `is_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            database::execute($sql);
            
            tcs_log("Created subscription matching rules table", self::$log_target);
            return true;
            
        } catch (\Exception $e) {
            tcs_log("Error creating rules table: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }
}
