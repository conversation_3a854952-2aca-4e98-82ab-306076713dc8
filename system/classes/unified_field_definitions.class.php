<?php

namespace system;

/**
 * Unified Field Definitions
 * 
 * Centralized field definition system that provides comprehensive metadata for all fields
 * used across subscription matching, data importing, and other systems.
 * 
 * This replaces the scattered field definitions in subscription_matching_rules and 
 * unified_field_mapper with a single source of truth.
 */
class unified_field_definitions {
    
    private static $log_target = 'unified_field_definitions';
    
    /**
     * Master field definitions with comprehensive metadata
     * Each field includes:
     * - label: Human-readable field name
     * - type: Data type (string, email, date, number, etc.)
     * - patterns: Array of possible field name variations
     * - normalized_fields: Standard field names to map to
     * - validation: Validation rules
     * - matching: Matching configuration for subscription matching
     * - description: Field description for UI
     * - category: Field category for organization
     */
    private static $field_definitions = [
        
        // === CORE IDENTIFICATION FIELDS ===
        'subscription_reference' => [
            'label' => 'Subscription Reference',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Unique subscription identifier or reference number',
            'patterns' => [
                'subscription_reference', 'subscription_id', 'agreement_number', 'contract_number',
                'license_key', 'serial_number', 'reference_number', 'subscription_number',
                'order_number', 'ref', 'reference', 'subs_subscriptionReferenceNumber'
            ],
            'normalized_fields' => ['subscription_reference', 'subs_subscriptionReferenceNumber', 'subscription_id'],
            'validation' => [
                'required' => false,
                'min_length' => 3,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 0, // Highest priority
                'confidence_score' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true,
                'fuzzy_matching' => false
            ]
        ],
        
        'email' => [
            'label' => 'Email Address',
            'type' => 'email',
            'category' => 'contact',
            'description' => 'Primary contact email address',
            'patterns' => [
                'email', 'email_address', 'contact_email', 'admin_email', 'customer_email',
                'end_customer_contact_email', 'subscription_contact_email', 'endcust_primary_admin_email',
                'primary_email', 'business_email', 'work_email'
            ],
            'normalized_fields' => ['email_address', 'endcust_primary_admin_email', 'end_customer_contact_email'],
            'validation' => [
                'required' => false,
                'format' => 'email'
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 1,
                'confidence_score' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true,
                'fuzzy_matching' => false
            ]
        ],
        
        'company_name' => [
            'label' => 'Company Name',
            'type' => 'string',
            'category' => 'organization',
            'description' => 'Organization or company name',
            'patterns' => [
                'company', 'company_name', 'customer', 'customer_name', 'client', 'client_name',
                'account_name', 'end_customer_name', 'endcust_name', 'organization', 'organization_name',
                'business_name', 'firm_name', 'sold_to_name', 'vendor_name', 'name'
            ],
            'normalized_fields' => ['company_name', 'endcust_name', 'end_customer_name'],
            'validation' => [
                'required' => false,
                'min_length' => 2,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 2,
                'confidence_threshold' => 70,
                'case_sensitive' => false,
                'exact_match_only' => false,
                'fuzzy_matching' => true,
                'similarity_threshold' => 70,
                'preprocessing' => [
                    'remove_common_words' => ['ltd', 'limited', 'inc', 'corp', 'corporation', 'llc', 'plc'],
                    'normalize_spaces' => true,
                    'remove_punctuation' => true
                ]
            ]
        ],
        
        // === PRODUCT INFORMATION ===
        'product_name' => [
            'label' => 'Product Name',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Software or product name',
            'patterns' => [
                'product', 'product_name', 'software', 'license', 'offering', 'product_family',
                'agreement_program_name', 'service', 'application', 'tool', 'subs_offeringName'
            ],
            'normalized_fields' => ['product_name', 'subs_offeringName', 'agreement_program_name'],
            'validation' => [
                'required' => false,
                'min_length' => 2,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => false, // Usually not used for matching
                'priority' => 10,
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true,
                'similarity_threshold' => 80
            ]
        ],
        
        'quantity' => [
            'label' => 'Quantity',
            'type' => 'number',
            'category' => 'product',
            'description' => 'Number of licenses or seats',
            'patterns' => [
                'quantity', 'qty', 'licenses', 'seats', 'subscription_quantity', 'users', 
                'count', 'license_count', 'seat_count', 'subs_quantity'
            ],
            'normalized_fields' => ['quantity', 'subs_quantity', 'subscription_quantity'],
            'validation' => [
                'required' => false,
                'min_value' => 0,
                'max_value' => 999999
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 90,
                'exact_match_only' => true
            ]
        ],
        
        // === DATE FIELDS ===
        'start_date' => [
            'label' => 'Start Date',
            'type' => 'date',
            'category' => 'dates',
            'description' => 'Subscription or agreement start date',
            'patterns' => [
                'start_date', 'date_start', 'purchase_date', 'agreement_start_date',
                'subscription_start_date', 'activation_date', 'begin_date', 'subs_startDate'
            ],
            'normalized_fields' => ['start_date', 'subs_startDate', 'agreement_start_date'],
            'validation' => [
                'required' => false,
                'format' => 'date'
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 95,
                'exact_match_only' => true
            ]
        ],
        
        'end_date' => [
            'label' => 'End Date',
            'type' => 'date',
            'category' => 'dates',
            'description' => 'Subscription or agreement end/expiry date',
            'patterns' => [
                'end_date', 'expiry_date', 'renewal_date', 'date_end', 'agreement_end_date',
                'subscription_end_date', 'expiration_date', 'due_date', 'subs_endDate'
            ],
            'normalized_fields' => ['end_date', 'subs_endDate', 'agreement_end_date'],
            'validation' => [
                'required' => false,
                'format' => 'date'
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 95,
                'exact_match_only' => true
            ]
        ],
        
        // === CONTACT INFORMATION ===
        'contact_name' => [
            'label' => 'Contact Name',
            'type' => 'string',
            'category' => 'contact',
            'description' => 'Primary contact person name',
            'patterns' => [
                'contact', 'contact_name', 'name', 'customer_contact', 'end_customer_contact_name',
                'subscription_contact_name', 'admin_name', 'primary_contact', 'first_name', 'last_name'
            ],
            'normalized_fields' => ['contact_name', 'end_customer_contact_name', 'subscription_contact_name'],
            'validation' => [
                'required' => false,
                'min_length' => 2,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => false, // Disabled by default (less reliable)
                'priority' => 3,
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true,
                'similarity_threshold' => 80
            ]
        ],
        
        // === STATUS FIELDS ===
        'status' => [
            'label' => 'Status',
            'type' => 'string',
            'category' => 'status',
            'description' => 'Subscription or license status',
            'patterns' => [
                'status', 'subscription_status', 'agreement_status', 'active',
                'license_status', 'account_status', 'subscription_state', 'license_state', 'subs_status'
            ],
            'normalized_fields' => ['status', 'subs_status', 'subscription_status'],
            'validation' => [
                'required' => false,
                'allowed_values' => ['active', 'inactive', 'expired', 'suspended', 'cancelled', 'pending']
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 90,
                'case_sensitive' => false
            ]
        ],

        // === ADDRESS FIELDS ===
        'address' => [
            'label' => 'Address',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Street address or business address',
            'patterns' => [
                'address', 'address_1', 'street', 'end_customer_address_1', 'business_address',
                'shipping_address', 'billing_address', 'order_shipping_address'
            ],
            'normalized_fields' => ['address', 'end_customer_address_1'],
            'validation' => [
                'required' => false,
                'max_length' => 500
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 70,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ]
        ],

        'city' => [
            'label' => 'City',
            'type' => 'string',
            'category' => 'address',
            'description' => 'City or town name',
            'patterns' => [
                'city', 'end_customer_city', 'town', 'shipping_city', 'billing_city', 'order_shipping_city'
            ],
            'normalized_fields' => ['city', 'end_customer_city'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ]
        ],

        'state' => [
            'label' => 'State/Province',
            'type' => 'string',
            'category' => 'address',
            'description' => 'State, province, or region',
            'patterns' => [
                'state', 'province', 'region', 'end_customer_state', 'shipping_state',
                'billing_state', 'order_shipping_state_province', 'state_province'
            ],
            'normalized_fields' => ['state', 'end_customer_state'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 90,
                'case_sensitive' => false
            ]
        ],

        'country' => [
            'label' => 'Country',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Country name',
            'patterns' => [
                'country', 'end_customer_country', 'nation', 'shipping_country',
                'billing_country', 'order_shipping_country'
            ],
            'normalized_fields' => ['country', 'end_customer_country'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 90,
                'case_sensitive' => false
            ]
        ],

        'postal_code' => [
            'label' => 'Postal Code',
            'type' => 'string',
            'category' => 'address',
            'description' => 'ZIP code or postal code',
            'patterns' => [
                'zip', 'postal_code', 'zip_code', 'postcode', 'end_customer_zip_code',
                'shipping_zip', 'billing_zip'
            ],
            'normalized_fields' => ['postal_code', 'end_customer_zip_code'],
            'validation' => [
                'required' => false,
                'max_length' => 20
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 95,
                'case_sensitive' => false
            ]
        ],

        // === PARTNER/RESELLER FIELDS ===
        'reseller_name' => [
            'label' => 'Reseller Name',
            'type' => 'string',
            'category' => 'partner',
            'description' => 'Reseller or partner company name',
            'patterns' => [
                'reseller', 'reseller_name', 'partner', 'partner_name',
                'account_primary_reseller_name', 'primary_reseller'
            ],
            'normalized_fields' => ['reseller_name', 'partner_name', 'account_primary_reseller_name'],
            'validation' => [
                'required' => false,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 70,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ]
        ],

        // === SERIAL NUMBERS AND IDENTIFIERS ===
        'serial_number' => [
            'label' => 'Serial Number',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Product or license serial number',
            'patterns' => [
                'serial', 'serial_number', 'serial_no', 'product_serial', 'license_serial'
            ],
            'normalized_fields' => ['serial_number', 'product_serial', 'license_serial'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
            ]
        ],

        // === FINANCIAL FIELDS ===
        'price' => [
            'label' => 'Price',
            'type' => 'currency',
            'category' => 'financial',
            'description' => 'Subscription or license price',
            'patterns' => [
                'price', 'cost', 'amount', 'subscription_price', 'license_cost',
                'total_price', 'unit_price', 'annual_price'
            ],
            'normalized_fields' => ['price', 'subscription_price', 'total_price'],
            'validation' => [
                'required' => false,
                'min_value' => 0
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 95,
                'exact_match_only' => true
            ]
        ],

        'currency' => [
            'label' => 'Currency',
            'type' => 'string',
            'category' => 'financial',
            'description' => 'Currency code (USD, EUR, etc.)',
            'patterns' => [
                'currency', 'currency_code', 'price_currency'
            ],
            'normalized_fields' => ['currency', 'currency_code'],
            'validation' => [
                'required' => false,
                'max_length' => 3,
                'allowed_values' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY']
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false
            ]
        ]
    ];
    
    /**
     * Get all field definitions
     */
    public static function get_all_fields(): array {
        return self::$field_definitions;
    }
    
    /**
     * Get field definition by name
     */
    public static function get_field(string $field_name): ?array {
        return self::$field_definitions[$field_name] ?? null;
    }
    
    /**
     * Get fields by category
     */
    public static function get_fields_by_category(string $category): array {
        $fields = [];
        foreach (self::$field_definitions as $name => $definition) {
            if ($definition['category'] === $category) {
                $fields[$name] = $definition;
            }
        }
        return $fields;
    }
    
    /**
     * Get all available categories
     */
    public static function get_categories(): array {
        $categories = [];
        foreach (self::$field_definitions as $definition) {
            $categories[] = $definition['category'];
        }
        return array_unique($categories);
    }
    
    /**
     * Get fields enabled for matching
     */
    public static function get_matching_fields(): array {
        $fields = [];
        foreach (self::$field_definitions as $name => $definition) {
            if ($definition['matching']['enabled'] ?? false) {
                $fields[$name] = $definition;
            }
        }
        return $fields;
    }
    
    /**
     * Find field by pattern match
     */
    public static function find_field_by_pattern(string $pattern): ?string {
        $pattern_lower = strtolower(trim($pattern));
        
        foreach (self::$field_definitions as $field_name => $definition) {
            $patterns_lower = array_map('strtolower', $definition['patterns']);
            if (in_array($pattern_lower, $patterns_lower)) {
                return $field_name;
            }
        }
        
        return null;
    }
    
    /**
     * Get normalized field names for a field
     */
    public static function get_normalized_fields(string $field_name): array {
        $definition = self::get_field($field_name);
        return $definition['normalized_fields'] ?? [];
    }
    
    /**
     * Get field patterns for a field
     */
    public static function get_field_patterns(string $field_name): array {
        $definition = self::get_field($field_name);
        return $definition['patterns'] ?? [];
    }
    
    /**
     * Get matching configuration for a field
     */
    public static function get_matching_config(string $field_name): array {
        $definition = self::get_field($field_name);
        return $definition['matching'] ?? [];
    }
    
    /**
     * Validate field value according to field definition
     */
    public static function validate_field_value(string $field_name, $value): array {
        $definition = self::get_field($field_name);
        if (!$definition) {
            return ['valid' => false, 'errors' => ['Field definition not found']];
        }
        
        $validation = $definition['validation'] ?? [];
        $errors = [];
        
        // Required check
        if (($validation['required'] ?? false) && empty($value)) {
            $errors[] = 'Field is required';
        }
        
        if (!empty($value)) {
            // Type-specific validation
            switch ($definition['type']) {
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = 'Invalid email format';
                    }
                    break;
                    
                case 'number':
                    if (!is_numeric($value)) {
                        $errors[] = 'Must be a number';
                    } else {
                        $num_value = (float)$value;
                        if (isset($validation['min_value']) && $num_value < $validation['min_value']) {
                            $errors[] = "Must be at least {$validation['min_value']}";
                        }
                        if (isset($validation['max_value']) && $num_value > $validation['max_value']) {
                            $errors[] = "Must be at most {$validation['max_value']}";
                        }
                    }
                    break;
                    
                case 'date':
                    if (!strtotime($value)) {
                        $errors[] = 'Invalid date format';
                    }
                    break;
                    
                case 'string':
                    $length = strlen($value);
                    if (isset($validation['min_length']) && $length < $validation['min_length']) {
                        $errors[] = "Must be at least {$validation['min_length']} characters";
                    }
                    if (isset($validation['max_length']) && $length > $validation['max_length']) {
                        $errors[] = "Must be at most {$validation['max_length']} characters";
                    }
                    break;
            }
            
            // Allowed values check
            if (isset($validation['allowed_values']) && !in_array(strtolower($value), array_map('strtolower', $validation['allowed_values']))) {
                $errors[] = 'Invalid value. Allowed: ' . implode(', ', $validation['allowed_values']);
            }
        }
        
        return ['valid' => empty($errors), 'errors' => $errors];
    }
}
