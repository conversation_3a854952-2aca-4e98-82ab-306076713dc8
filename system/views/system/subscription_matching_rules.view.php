<?php
/**
 * Subscription Matching Rules Manager
 */

use system\subscription_matching_rules;
use system\unified_field_definitions;

// Get current rules
$rules = subscription_matching_rules::get_rules();
$stats = subscription_matching_rules::get_rule_statistics();

// Get field definitions for display
$all_fields = unified_field_definitions::get_all_fields();
$categories = unified_field_definitions::get_categories();

?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Subscription Matching Rules</h1>
                <p class="mt-2 text-sm text-gray-600">Configure how the system matches CSV subscription data with Autodesk customers</p>
            </div>
            <div class="flex space-x-3">
                <button type="button" 
                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        hx-post="<?= APP_ROOT ?>/api/subscription_matching_rules/reset_defaults"
                        hx-confirm="Are you sure you want to reset all rules to defaults? This will overwrite any custom configurations."
                        hx-target="#rules-container"
                        hx-swap="outerHTML">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset to Defaults
                </button>
                <button type="button" 
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
                        hx-get="<?= APP_ROOT ?>/api/subscription_matching_rules/test"
                        hx-target="#test-results"
                        hx-swap="innerHTML">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Test Rules
                </button>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-3">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Rules</dt>
                                <dd class="text-lg font-medium text-gray-900"><?= $stats['total_rules'] ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Enabled</dt>
                                <dd class="text-lg font-medium text-gray-900"><?= $stats['enabled_rules'] ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gray-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Disabled</dt>
                                <dd class="text-lg font-medium text-gray-900"><?= $stats['disabled_rules'] ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Field Definitions -->
    <div class="mb-8">
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">Available Field Definitions</h2>
                        <p class="mt-1 text-sm text-gray-600">
                            Centralized field definitions used across subscription matching, data importing, and other systems.
                        </p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="text-sm text-gray-500"><?= count($all_fields) ?> fields defined</span>
                        <button type="button"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                                hx-get="<?= APP_ROOT ?>/api/unified_field_definitions?action=edit_form&is_new=true"
                                hx-target="body"
                                hx-swap="beforeend">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            New Field
                        </button>
                    </div>
                </div>
            </div>

            <div class="px-6 py-4" x-data="{ activeCategory: '<?= $categories[0] ?? 'identification' ?>' }">
                <!-- Category Tabs -->
                <div class="border-b border-gray-200 mb-4">
                    <nav class="-mb-px flex space-x-8">
                        <?php foreach ($categories as $index => $category): ?>
                            <button type="button"
                                    class="py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                                    :class="activeCategory === '<?= $category ?>' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    @click="activeCategory = '<?= $category ?>'">
                                <?= ucwords(str_replace('_', ' ', $category)) ?>
                            </button>
                        <?php endforeach; ?>
                    </nav>
                </div>

                <!-- Field Definitions by Category -->
                <?php foreach ($categories as $index => $category): ?>
                    <div x-show="activeCategory === '<?= $category ?>'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php
                            $category_fields = unified_field_definitions::get_fields_by_category($category);
                            foreach ($category_fields as $field_name => $definition):
                                $is_custom = unified_field_definitions::is_custom_field($field_name);
                                $is_modified = unified_field_definitions::is_modified_field($field_name);
                            ?>
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-medium text-gray-900"><?= $definition['label'] ?></h4>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                                <?= $definition['type'] ?>
                                            </span>
                                            <button type="button"
                                                    class="text-indigo-600 hover:text-indigo-900 text-xs"
                                                    hx-get="<?= APP_ROOT ?>/api/unified_field_definitions?action=edit_form&field_name=<?= urlencode($field_name) ?>"
                                                    hx-target="body"
                                                    hx-swap="beforeend"
                                                    title="Edit field definition">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <p class="text-sm text-gray-600 mb-3"><?= $definition['description'] ?></p>

                                    <!-- Status Indicators -->
                                    <?php if ($is_custom || $is_modified): ?>
                                        <div class="flex items-center space-x-2 mb-3">
                                            <?php if ($is_custom): ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                    Custom Field
                                                </span>
                                            <?php elseif ($is_modified): ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                    Modified
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>

                                    <div class="space-y-2">
                                        <div>
                                            <span class="text-xs font-medium text-gray-700">Patterns:</span>
                                            <div class="flex flex-wrap gap-1 mt-1">
                                                <?php foreach (array_slice($definition['patterns'], 0, 3) as $pattern): ?>
                                                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-blue-100 text-blue-800">
                                                        <?= htmlspecialchars($pattern) ?>
                                                    </span>
                                                <?php endforeach; ?>
                                                <?php if (count($definition['patterns']) > 3): ?>
                                                    <span class="text-xs text-gray-500">+<?= count($definition['patterns']) - 3 ?> more</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <?php if ($definition['matching']['enabled'] ?? false): ?>
                                            <div class="flex items-center text-xs">
                                                <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                                                <span class="text-green-700">Enabled for matching (Priority: <?= $definition['matching']['priority'] ?>)</span>
                                            </div>
                                        <?php else: ?>
                                            <div class="flex items-center text-xs">
                                                <span class="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                                                <span class="text-gray-500">Not used for matching</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Rules List -->
    <div id="rules-container" class="space-y-6">
        <?php foreach ($rules as $rule_name => $rule): ?>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <h3 class="text-lg font-medium text-gray-900">
                                <?= ucwords(str_replace('_', ' ', $rule_name)) ?>
                            </h3>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $rule['enabled'] ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' ?>">
                                <?= $rule['enabled'] ? 'Enabled' : 'Disabled' ?>
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Priority: <?= $rule['priority'] ?>
                            </span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button type="button" 
                                    class="text-sm text-indigo-600 hover:text-indigo-900"
                                    hx-get="<?= APP_ROOT ?>/api/subscription_matching_rules/edit?rule=<?= urlencode($rule_name) ?>"
                                    hx-target="#rule-editor"
                                    hx-swap="innerHTML">
                                Edit
                            </button>
                            <button type="button" 
                                    class="text-sm <?= $rule['enabled'] ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900' ?>"
                                    hx-post="<?= APP_ROOT ?>/api/subscription_matching_rules/toggle"
                                    hx-vals='{"rule_name": "<?= $rule_name ?>", "enabled": <?= $rule['enabled'] ? 'false' : 'true' ?>}'
                                    hx-target="#rules-container"
                                    hx-swap="outerHTML">
                                <?= $rule['enabled'] ? 'Disable' : 'Enable' ?>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Field Patterns</h4>
                            <div class="flex flex-wrap gap-1">
                                <?php foreach ($rule['field_patterns'] as $pattern): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                        <?= htmlspecialchars($pattern) ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Configuration</h4>
                            <dl class="text-sm text-gray-600 space-y-1">
                                <?php if (isset($rule['confidence_score'])): ?>
                                    <div class="flex justify-between">
                                        <dt>Confidence Score:</dt>
                                        <dd><?= $rule['confidence_score'] ?>%</dd>
                                    </div>
                                <?php endif; ?>
                                <?php if (isset($rule['similarity_threshold'])): ?>
                                    <div class="flex justify-between">
                                        <dt>Similarity Threshold:</dt>
                                        <dd><?= $rule['similarity_threshold'] ?>%</dd>
                                    </div>
                                <?php endif; ?>
                                <?php if (isset($rule['fuzzy_matching'])): ?>
                                    <div class="flex justify-between">
                                        <dt>Fuzzy Matching:</dt>
                                        <dd><?= $rule['fuzzy_matching'] ? 'Yes' : 'No' ?></dd>
                                    </div>
                                <?php endif; ?>
                                <?php if (isset($rule['case_sensitive'])): ?>
                                    <div class="flex justify-between">
                                        <dt>Case Sensitive:</dt>
                                        <dd><?= $rule['case_sensitive'] ? 'Yes' : 'No' ?></dd>
                                    </div>
                                <?php endif; ?>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Rule Editor Modal Placeholder -->
    <div id="rule-editor"></div>
    
    <!-- Test Results Placeholder -->
    <div id="test-results" class="mt-8"></div>
</div>

<script>
// Auto-refresh rules every 30 seconds to show any changes
// This is acceptable traditional JavaScript as it's a background task that HTMX cannot handle efficiently
setInterval(function() {
    if (!document.querySelector('#rule-editor').innerHTML) {
        htmx.trigger('#rules-container', 'refresh');
    }
}, 30000);
</script>
