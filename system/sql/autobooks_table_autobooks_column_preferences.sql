
-- --------------------------------------------------------

--
-- Table structure for table `autobooks_column_preferences`
--

CREATE TABLE `autobooks_column_preferences` (
  `id` int(10) UNSIGNED NOT NULL,
  `table_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Data table identifier',
  `user_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'User ID (NULL for global preferences)',
  `data_source_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'Associated data source ID',
  `hidden_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of hidden column IDs',
  `column_structure` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of column structure configuration',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Column preferences for data tables';
