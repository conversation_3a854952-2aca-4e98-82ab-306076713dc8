<?php

use system\unified_field_definitions;

/**
 * API endpoints for managing unified field definitions
 */

// Get all field definitions
if ($_SERVER['REQUEST_METHOD'] === 'GET' && !isset($_GET['action'])) {
    try {
        $fields = unified_field_definitions::get_all_fields();
        $stats = unified_field_definitions::get_field_statistics();
        
        echo json_encode([
            'success' => true,
            'fields' => $fields,
            'statistics' => $stats
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to load field definitions: ' . $e->getMessage()
        ]);
    }
    exit;
}

// Get single field definition
if ($_SERVER['REQUEST_METHOD'] === 'GET' && $_GET['action'] === 'get_field') {
    try {
        $field_name = $_GET['field_name'] ?? '';
        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }
        
        $field = unified_field_definitions::get_field($field_name);
        if (!$field) {
            throw new Exception('Field not found');
        }
        
        echo json_encode([
            'success' => true,
            'field' => $field,
            'is_custom' => unified_field_definitions::is_custom_field($field_name),
            'is_modified' => unified_field_definitions::is_modified_field($field_name)
        ]);
    } catch (Exception $e) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit;
}

// Create new field definition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_GET['action'] === 'create') {
    try {
        $field_name = $_POST['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Check if field already exists
        if (unified_field_definitions::get_field($field_name)) {
            throw new Exception('Field already exists');
        }

        // Build definition from form data
        $definition = [
            'label' => $_POST['label'] ?? '',
            'type' => $_POST['type'] ?? 'string',
            'category' => $_POST['category'] ?? 'custom',
            'description' => $_POST['description'] ?? '',
            'patterns' => array_filter(explode("\n", $_POST['patterns'] ?? ''), 'trim'),
            'normalized_fields' => array_filter(explode("\n", $_POST['normalized_fields'] ?? ''), 'trim'),
            'validation' => [
                'required' => false
            ],
            'matching' => [
                'enabled' => isset($_POST['matching_enabled']),
                'priority' => (int)($_POST['matching_priority'] ?? 10),
                'case_sensitive' => isset($_POST['case_sensitive']),
                'fuzzy_matching' => isset($_POST['fuzzy_matching']),
                'similarity_threshold' => (int)($_POST['similarity_threshold'] ?? 70),
                'exact_match_only' => !isset($_POST['fuzzy_matching'])
            ]
        ];

        // Validate definition
        $validation = unified_field_definitions::validate_field_definition($definition);
        if (!$validation['valid']) {
            throw new Exception('Invalid field definition: ' . implode(', ', $validation['errors']));
        }

        // Save field definition
        $success = unified_field_definitions::save_field_definition($field_name, $definition);
        if (!$success) {
            throw new Exception('Failed to save field definition');
        }

        // Return success message and close modal
        echo '<script>
            document.getElementById("field-editor-modal").remove();
            window.location.reload();
        </script>';

    } catch (Exception $e) {
        // Return error message in modal
        echo '<div class="p-4 bg-red-50 border border-red-200 rounded-md mb-4">';
        echo '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';

        // Re-include the form with error
        $field = unified_field_definitions::create_field_template($field_name ?? 'new_field');
        $categories = unified_field_definitions::get_categories();
        $types = ['string', 'email', 'date', 'number', 'currency'];
        $is_new = true;
        $form_title = 'Create New Field Definition';
        $submit_action = 'create';
        include __DIR__ . '/../views/system/field_definition_edit_form.view.php';
    }
    exit;
}

// Update field definition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_GET['action'] === 'update') {
    try {
        $field_name = $_POST['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Build definition from form data
        $definition = [
            'label' => $_POST['label'] ?? '',
            'type' => $_POST['type'] ?? 'string',
            'category' => $_POST['category'] ?? 'custom',
            'description' => $_POST['description'] ?? '',
            'patterns' => array_filter(explode("\n", $_POST['patterns'] ?? ''), 'trim'),
            'normalized_fields' => array_filter(explode("\n", $_POST['normalized_fields'] ?? ''), 'trim'),
            'validation' => [
                'required' => false
            ],
            'matching' => [
                'enabled' => isset($_POST['matching_enabled']),
                'priority' => (int)($_POST['matching_priority'] ?? 10),
                'case_sensitive' => isset($_POST['case_sensitive']),
                'fuzzy_matching' => isset($_POST['fuzzy_matching']),
                'similarity_threshold' => (int)($_POST['similarity_threshold'] ?? 70),
                'exact_match_only' => !isset($_POST['fuzzy_matching'])
            ]
        ];

        // Validate definition
        $validation = unified_field_definitions::validate_field_definition($definition);
        if (!$validation['valid']) {
            throw new Exception('Invalid field definition: ' . implode(', ', $validation['errors']));
        }

        // Save field definition
        $success = unified_field_definitions::save_field_definition($field_name, $definition);
        if (!$success) {
            throw new Exception('Failed to update field definition');
        }

        // Return success message and close modal
        echo '<script>
            document.getElementById("field-editor-modal").remove();
            window.location.reload();
        </script>';

    } catch (Exception $e) {
        // Return error message in modal
        echo '<div class="p-4 bg-red-50 border border-red-200 rounded-md mb-4">';
        echo '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';

        // Re-include the form with error
        $field = unified_field_definitions::get_field($field_name);
        $categories = unified_field_definitions::get_categories();
        $types = ['string', 'email', 'date', 'number', 'currency'];
        $is_new = false;
        $form_title = 'Edit Field Definition: ' . $field_name;
        $submit_action = 'update';
        include __DIR__ . '/../views/system/field_definition_edit_form.view.php';
    }
    exit;
}

// Delete field definition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_GET['action'] === 'delete') {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $field_name = $input['field_name'] ?? '';
        
        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }
        
        // Check if field exists
        if (!unified_field_definitions::get_field($field_name)) {
            throw new Exception('Field not found');
        }
        
        // Delete field definition
        $success = unified_field_definitions::delete_field_definition($field_name);
        if (!$success) {
            throw new Exception('Failed to delete field definition');
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Field definition deleted successfully'
        ]);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit;
}

// Reset field to default
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_GET['action'] === 'reset') {
    try {
        $field_name = $_POST['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Reset field definition
        $success = unified_field_definitions::reset_field_to_default($field_name);
        if (!$success) {
            throw new Exception('Failed to reset field definition');
        }

        // Return success message and close modal
        echo '<script>
            document.getElementById("field-editor-modal").remove();
            window.location.reload();
        </script>';

    } catch (Exception $e) {
        // Return error message
        echo '<div class="p-4 bg-red-50 border border-red-200 rounded-md">';
        echo '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
    exit;
}

// Get field editor form
if ($_SERVER['REQUEST_METHOD'] === 'GET' && $_GET['action'] === 'edit_form') {
    try {
        $field_name = $_GET['field_name'] ?? '';
        $is_new = $_GET['is_new'] ?? false;
        
        if (!$is_new && empty($field_name)) {
            throw new Exception('Field name is required');
        }
        
        $field = null;
        if (!$is_new) {
            $field = unified_field_definitions::get_field($field_name);
            if (!$field) {
                throw new Exception('Field not found');
            }
        } else {
            $field = unified_field_definitions::create_field_template('new_field');
        }
        
        $categories = unified_field_definitions::get_categories();
        $types = ['string', 'email', 'date', 'number', 'currency'];
        
        // Include the edit form template
        include __DIR__ . '/../views/system/field_definition_edit_form.view.php';
        
    } catch (Exception $e) {
        echo '<div class="p-4 bg-red-50 border border-red-200 rounded-md">';
        echo '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
    exit;
}

// Invalid action
http_response_code(400);
echo json_encode([
    'success' => false,
    'error' => 'Invalid action or method'
]);
